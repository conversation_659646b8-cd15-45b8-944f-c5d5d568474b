﻿using Microsoft.AspNetCore.Http;
using SuperCareApp.Application.Common.Interfaces.Documents;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Documents;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Documents.Commands;

/// <summary>
/// Command to upload a document
/// </summary>
/// <param name="File">The file to upload</param>
/// <param name="DocumentType">Type of document</param>
/// <param name="UserId">ID of the user uploading the document</param>
/// <param name="Country">Country associated with the certification</param>
/// <param name="CertificationType">Type of certification</param>
/// <param name="OtherCertificationType">Custom certification type (if "Other" is selected)</param>
/// <param name="CertificationNumber">Certification number or identifier</param>
/// <param name="ExpiryDate">Expiry date of the certification</param>
public record UploadDocumentCommand(
    IFormFile File,
    string DocumentType,
    Guid UserId,
    string Issuer,
    string Country,
    string CertificationType,
    string? OtherCertificationType,
    string? CertificationNumber,
    DateTime? ExpiryDate
) : ICommand<Result<DocumentResponse>>;

/// <summary>
/// Handler for the UploadDocumentCommand
/// </summary>
internal sealed class UploadDocumentCommandHandler
    : ICommandHandler<UploadDocumentCommand, Result<DocumentResponse>>
{
    private readonly IDocumentService _documentService;
    private readonly ILogger<UploadDocumentCommandHandler> _logger;

    public UploadDocumentCommandHandler(
        IDocumentService documentService,
        ILogger<UploadDocumentCommandHandler> logger
    )
    {
        _documentService = documentService;
        _logger = logger;
    }

    public async Task<Result<DocumentResponse>> Handle(
        UploadDocumentCommand request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var result = await _documentService.UploadDocumentAsync(
                request.File,
                request.DocumentType,
                request.UserId,
                request.Issuer,
                request.Country,
                request.CertificationType,
                request.OtherCertificationType,
                request.CertificationNumber,
                request.ExpiryDate
            );

            if (result.IsFailure)
            {
                return Result.Failure<DocumentResponse>(result.Error);
            }

            // Create a response with the document details
            var response = new DocumentResponse
            {
                DocumentId = result.Value.DocumentId,
                UserId = result.Value.UserId,
                FileName = result.Value.FileName,
                DocumentType = request.DocumentType,
                VerificationStatus = VerificationStatus.Pending.ToString(),
                UploadedAt = DateTime.UtcNow,
                Country = request.Country,
                CertificationType = request.CertificationType,
                OtherCertificationType = request.OtherCertificationType,
                CertificationNumber = request.CertificationNumber,
                ExpiryDate = request.ExpiryDate,
                IsExpired =
                    request.ExpiryDate.HasValue && request.ExpiryDate.Value < DateTime.UtcNow,
            };

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading document");
            return Result.Failure<DocumentResponse>(
                Error.Internal("An error occurred while uploading the document")
            );
        }
    }
}
