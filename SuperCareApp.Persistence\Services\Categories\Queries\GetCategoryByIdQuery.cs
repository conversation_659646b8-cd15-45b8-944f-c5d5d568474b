﻿using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Categories;

namespace SuperCareApp.Persistence.Services.Categories.Queries
{
    /// <summary>
    /// Query to get a care category by ID
    /// </summary>
    public record GetCategoryByIdQuery(Guid CategoryId) : IQuery<Result<CareCategoryResponse>>;

    /// <summary>
    /// Handler for the GetCategoryByIdQuery
    /// </summary>
    internal sealed class GetCategoryByIdQueryHandler
        : IQueryHandler<GetCategoryByIdQuery, Result<CareCategoryResponse>>
    {
        private readonly ICareCategoryService _categoryService;
        private readonly ILogger<GetCategoryByIdQueryHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public GetCategoryByIdQueryHandler(
            ICareCategoryService categoryService,
            ILogger<GetCategoryByIdQueryHandler> logger
        )
        {
            _categoryService = categoryService;
            _logger = logger;
        }

        /// <summary>
        /// Handles the query
        /// </summary>
        public async Task<Result<CareCategoryResponse>> Handle(
            GetCategoryByIdQuery request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                return await _categoryService.GetCategoryByIdAsync(
                    request.CategoryId,
                    cancellationToken
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error getting care category with ID {CategoryId}",
                    request.CategoryId
                );
                return Result.Failure<CareCategoryResponse>(Error.Internal(ex.Message));
            }
        }
    }
}
