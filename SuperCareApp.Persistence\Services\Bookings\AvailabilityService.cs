using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Application.Common.Settings;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Bookings
{
    public class AvailabilityService : IAvailabilityService
    {
        private readonly ApplicationDbContext _context;
        private readonly IBookingManagementService _scheduleService;
        private readonly ILogger<AvailabilityService> _logger;

        public AvailabilityService(
            ApplicationDbContext context,
            IBookingManagementService scheduleService,
            ILogger<AvailabilityService> logger
        )
        {
            _context = context;
            _scheduleService = scheduleService;
            _logger = logger;
        }

        public async Task<Result<Guid>> AddAvailabilityAsync(
            Guid userId,
            string dayOfWeek,
            bool isAvailable,
            List<AvailabilitySlot> slots
        )
        {
            try
            {
                // Validate DayOfWeek
                if (!Enum.TryParse<DayOfWeek>(dayOfWeek, true, out _))
                    return Result.Failure<Guid>(Error.Validation("Invalid day of week"));

                var providerProfile = await _context
                    .CareProviderProfiles.AsNoTracking()
                    .FirstOrDefaultAsync(p => p.UserId == userId && !p.IsDeleted);
                if (providerProfile == null)
                    return Result.Failure<Guid>(Error.NotFound("Provider profile not found"));

                // Validate slots
                foreach (var slot in slots)
                {
                    var interval = new Interval<TimeOnly>(slot.StartTime, slot.EndTime);
                    try
                    {
                        interval.Validate();
                    }
                    catch (ArgumentException ex)
                    {
                        return Result.Failure<Guid>(Error.Validation(ex.Message));
                    }
                }

                var availability = new Availability
                {
                    Id = Guid.NewGuid(),
                    ProviderId = providerProfile.Id,
                    DayOfWeek = dayOfWeek,
                    IsAvailable = isAvailable,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = userId,
                };

                await _context.Availabilities.AddAsync(availability);

                foreach (var slot in slots)
                {
                    var availabilitySlot = new AvailabilitySlot
                    {
                        Id = Guid.NewGuid(),
                        AvailabilityId = availability.Id,
                        StartTime = slot.StartTime,
                        EndTime = slot.EndTime,
                    };
                    await _context.AvailabilitySlots.AddAsync(availabilitySlot);
                }

                await _context.SaveChangesAsync();
                return Result.Success(availability.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding availability");
                return Result.Failure<Guid>(Error.Internal("Error adding availability"));
            }
        }

        public async Task<Result> UpdateAvailabilityAsync(
            Guid userId,
            Guid availabilityId,
            bool isAvailable,
            List<AvailabilitySlot> slots
        )
        {
            try
            {
                var providerProfile = await _context
                    .CareProviderProfiles.AsNoTracking()
                    .FirstOrDefaultAsync(p => p.UserId == userId && !p.IsDeleted);
                if (providerProfile == null)
                    return Result.Failure(Error.NotFound("Provider profile not found"));

                var availability = await _context
                    .Availabilities.Include(a => a.AvailabilitySlots)
                    .FirstOrDefaultAsync(a =>
                        a.Id == availabilityId && a.ProviderId == providerProfile.Id
                    );
                if (availability == null)
                    return Result.Failure(
                        Error.NotFound(
                            "Availability not found or you don't have permission to update it"
                        )
                    );

                // Validate slots
                foreach (var slot in slots)
                {
                    var interval = new Interval<TimeOnly>(slot.StartTime, slot.EndTime);
                    try
                    {
                        interval.Validate();
                    }
                    catch (ArgumentException ex)
                    {
                        return Result.Failure(Error.Validation(ex.Message));
                    }
                }

                availability.IsAvailable = isAvailable;
                _context.AvailabilitySlots.RemoveRange(availability.AvailabilitySlots);

                foreach (var slot in slots)
                {
                    var availabilitySlot = new AvailabilitySlot
                    {
                        Id = Guid.NewGuid(),
                        AvailabilityId = availability.Id,
                        StartTime = slot.StartTime,
                        EndTime = slot.EndTime,
                    };
                    await _context.AvailabilitySlots.AddAsync(availabilitySlot);
                }

                availability.UpdatedAt = DateTime.UtcNow;
                availability.UpdatedBy = userId;

                await _context.SaveChangesAsync();
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating availability");
                return Result.Failure(Error.Internal("Error updating availability"));
            }
        }

        public async Task<Result> DeleteAvailabilityAsync(Guid userId, Guid availabilityId)
        {
            try
            {
                var providerProfile = await _context
                    .CareProviderProfiles.AsNoTracking()
                    .FirstOrDefaultAsync(p => p.UserId == userId && !p.IsDeleted);
                if (providerProfile == null)
                    return Result.Failure(Error.NotFound("Provider profile not found"));

                var availability = await _context
                    .Availabilities.Include(a => a.AvailabilitySlots)
                    .FirstOrDefaultAsync(a =>
                        a.Id == availabilityId && a.ProviderId == providerProfile.Id
                    );
                if (availability == null)
                    return Result.Failure(
                        Error.NotFound(
                            "Availability not found or you don't have permission to delete it"
                        )
                    );

                availability.IsDeleted = true;
                availability.DeletedAt = DateTime.UtcNow;
                availability.DeletedBy = userId;
                await _context.SaveChangesAsync();
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting availability");
                return Result.Failure(Error.Internal("Error deleting availability"));
            }
        }

        public async Task<Result> BulkUpdateAvailabilityAsync(
            Guid providerId,
            List<(
                Guid AvailabilityId,
                bool IsAvailable,
                List<AvailabilitySlot> Slots
            )> availabilityUpdates
        )
        {
            try
            {
                var providerProfile = await _context.CareProviderProfiles.FirstOrDefaultAsync(p =>
                    p.Id == providerId && !p.IsDeleted
                );
                if (providerProfile == null)
                    return Result.Failure(Error.NotFound("Provider profile not found"));

                using var transaction = await _context.Database.BeginTransactionAsync();

                foreach (var update in availabilityUpdates)
                {
                    var availability = await _context
                        .Availabilities.Include(a => a.AvailabilitySlots)
                        .FirstOrDefaultAsync(a =>
                            a.Id == update.AvailabilityId && a.ProviderId == providerProfile.Id
                        );
                    if (availability == null)
                    {
                        await transaction.RollbackAsync();
                        return Result.Failure(
                            Error.NotFound(
                                $"Availability with ID {update.AvailabilityId} not found or you don't have permission to update it"
                            )
                        );
                    }

                    // Validate slots
                    foreach (var slot in update.Slots)
                    {
                        var interval = new Interval<TimeOnly>(slot.StartTime, slot.EndTime);
                        try
                        {
                            interval.Validate();
                        }
                        catch (ArgumentException ex)
                        {
                            await transaction.RollbackAsync();
                            return Result.Failure(Error.Validation(ex.Message));
                        }
                    }

                    availability.IsAvailable = update.IsAvailable;
                    _context.AvailabilitySlots.RemoveRange(availability.AvailabilitySlots);

                    foreach (var slot in update.Slots)
                    {
                        var availabilitySlot = new AvailabilitySlot
                        {
                            Id = Guid.NewGuid(),
                            AvailabilityId = availability.Id,
                            StartTime = slot.StartTime,
                            EndTime = slot.EndTime,
                        };
                        await _context.AvailabilitySlots.AddAsync(availabilitySlot);
                    }

                    availability.UpdatedAt = DateTime.UtcNow;
                    availability.UpdatedBy = providerId;
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during bulk update of availabilities");
                return Result.Failure(Error.Internal("Error updating availabilities"));
            }
        }

        public async Task<Result> BulkUpdateAvailabilityByDayOfWeekAsync(
            Guid providerId,
            List<(
                string DayOfWeek,
                bool IsAvailable,
                List<AvailabilitySlot> Slots
            )> availabilityUpdates,
            int bufferDuration,
            bool? providesRecurringBooking,
            int? workingHoursPerDay
        )
        {
            try
            {
                var providerProfile = await _context.CareProviderProfiles.FirstOrDefaultAsync(p =>
                    p.Id == providerId && !p.IsDeleted
                );
                if (providerProfile == null)
                    return Result.Failure(Error.NotFound("Provider profile not found"));

                if (bufferDuration < 0)
                    return Result.Failure(Error.Validation("Buffer duration cannot be negative"));

                if (workingHoursPerDay < 0)
                    return Result.Failure(
                        Error.Validation("Working hours per day cannot be negative")
                    );

                //await using var transaction = await _context.Database.BeginTransactionAsync();

                providerProfile.BufferDuration = bufferDuration;
                providerProfile.WorkingHours = workingHoursPerDay;
                _context.Update(providerProfile);

                foreach (var update in availabilityUpdates)
                {
                    if (!Enum.TryParse<DayOfWeek>(update.DayOfWeek, true, out _))
                        return Result.Failure(
                            Error.Validation($"Invalid day of week: {update.DayOfWeek}")
                        );

                    // Validate slots
                    foreach (var slot in update.Slots)
                    {
                        var interval = new Interval<TimeOnly>(slot.StartTime, slot.EndTime);
                        try
                        {
                            interval.Validate();
                        }
                        catch (ArgumentException ex)
                        {
                            //await transaction.RollbackAsync();
                            return Result.Failure(Error.Validation(ex.Message));
                        }
                    }

                    var availability = await _context
                        .Availabilities.Include(a => a.AvailabilitySlots)
                        .FirstOrDefaultAsync(a =>
                            a.ProviderId == providerId
                            && a.DayOfWeek == update.DayOfWeek
                            && !a.IsDeleted
                        );

                    if (availability != null)
                    {
                        availability.IsAvailable = update.IsAvailable;
                        if (update.IsAvailable)
                        {
                            _context.AvailabilitySlots.RemoveRange(availability.AvailabilitySlots);

                            foreach (var slot in update.Slots)
                            {
                                var availabilitySlot = new AvailabilitySlot
                                {
                                    Id = Guid.NewGuid(),
                                    AvailabilityId = availability.Id,
                                    StartTime = slot.StartTime,
                                    EndTime = slot.EndTime,
                                };
                                await _context.AvailabilitySlots.AddAsync(availabilitySlot);
                            }
                        }
                        availability.UpdatedAt = DateTime.UtcNow;
                        availability.UpdatedBy = providerId;
                        // No need for SaveChangesAsync here; it's handled at the end of the transaction.
                    }
                    else
                    {
                        var newAvailability = new Availability
                        {
                            Id = Guid.NewGuid(),
                            ProviderId = providerId,
                            DayOfWeek = update.DayOfWeek,
                            IsAvailable = update.IsAvailable,
                            CreatedAt = DateTime.UtcNow,
                            CreatedBy = providerId,
                        };
                        await _context.Availabilities.AddAsync(newAvailability);
                        if (update.IsAvailable)
                        {
                            await _context.SaveChangesAsync();

                            foreach (var slot in update.Slots)
                            {
                                var availabilitySlot = new AvailabilitySlot
                                {
                                    Id = Guid.NewGuid(),
                                    AvailabilityId = newAvailability.Id,
                                    StartTime = slot.StartTime,
                                    EndTime = slot.EndTime,
                                };
                                await _context.AvailabilitySlots.AddAsync(availabilitySlot);
                            }
                        }
                    }
                }

                await _context.SaveChangesAsync();
                //await transaction.CommitAsync();
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk updating availabilities by day of week");
                return Result.Failure(Error.Internal("Error updating availabilities"));
            }
        }

        public async Task<Result<IEnumerable<AvailabilityResponse>>> GetProviderAvailabilityAsync(
            Guid providerId
        )
        {
            try
            {
                var availabilities = await _context
                    .Availabilities.Where(a => a.ProviderId == providerId && !a.IsDeleted)
                    .Include(a => a.AvailabilitySlots)
                    .OrderBy(a => a.DayOfWeek)
                    .AsNoTracking()
                    .ToListAsync();

                var currentDate = DateTime.UtcNow.Date;
                var activeLeavePeriods = await _context
                    .Leaves.Where(l =>
                        l.ProviderId == providerId
                        && !l.IsDeleted
                        && l.StartDate <= currentDate.AddDays(7)
                        && l.EndDate >= currentDate
                    )
                    .AsNoTracking()
                    .ToListAsync();

                var response = availabilities.Select(a =>
                {
                    var dayOfWeek = Enum.Parse<DayOfWeek>(a.DayOfWeek);
                    var isOnLeaveForThisDay = activeLeavePeriods.Any(leave =>
                    {
                        for (
                            var date = leave.StartDate;
                            date <= leave.EndDate;
                            date = date.AddDays(1)
                        )
                        {
                            if (date.DayOfWeek == dayOfWeek && date >= currentDate)
                                return true;
                        }

                        return false;
                    });

                    return new AvailabilityResponse
                    {
                        Id = a.Id,
                        Day = a.DayOfWeek,
                        Available = a.IsAvailable && !isOnLeaveForThisDay,
                        Slots =
                            a.IsAvailable && !isOnLeaveForThisDay
                                ? a
                                    .AvailabilitySlots.Select(s => new TimeSlotResponse
                                    {
                                        StartTime = s.StartTime.ToString("HH:mm"),
                                        EndTime = s.EndTime.ToString("HH:mm"),
                                    })
                                    .ToList()
                                : new List<TimeSlotResponse>(),
                    };
                });

                return Result.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving provider availability");
                return Result.Failure<IEnumerable<AvailabilityResponse>>(
                    Error.Internal("Error retrieving provider availability")
                );
            }
        }

        public async Task<Result<IEnumerable<AvailabilityResponse>>> GetMyAvailabilityAsync(
            Guid userId
        )
        {
            try
            {
                var providerProfile = await _context
                    .CareProviderProfiles.AsNoTracking()
                    .FirstOrDefaultAsync(p => p.UserId == userId && !p.IsDeleted);
                if (providerProfile == null)
                    return Result.Failure<IEnumerable<AvailabilityResponse>>(
                        Error.NotFound("Provider profile not found")
                    );

                return await GetProviderAvailabilityAsync(providerProfile.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving current provider availability");
                return Result.Failure<IEnumerable<AvailabilityResponse>>(
                    Error.Internal("Error retrieving availability")
                );
            }
        }

        public async Task<Result<IEnumerable<TimeSlotResponse>>> GetAvailableTimeSlotsAsync(
            Guid providerId,
            DateTime date,
            int durationMinutes = 60
        )
        {
            try
            {
                var dateOnly = DateOnly.FromDateTime(date);
                var availableSlots = await _scheduleService.GetAvailableSlotsForDateAsync(
                    providerId,
                    dateOnly
                );
                var slotDuration = TimeSpan.FromMinutes(durationMinutes);

                var timeSlots = availableSlots
                    .Where(slot => (slot.End - slot.Start) >= slotDuration)
                    .Select(slot => new TimeSlotResponse
                    {
                        StartTime = slot.Start.ToString("HH:mm"),
                        EndTime = slot.End.ToString("HH:mm"),
                    })
                    .OrderBy(s => s.StartTime)
                    .ToList();

                return Result.Success<IEnumerable<TimeSlotResponse>>(timeSlots);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving available time slots");
                return Result.Failure<IEnumerable<TimeSlotResponse>>(
                    Error.Internal("Error retrieving available time slots")
                );
            }
        }

        public async Task<Result<AvailabilityResponse>> GetProviderAvailabilityForDateAsync(
            Guid providerId,
            DateTime date
        )
        {
            try
            {
                var dayOfWeekStr = date.DayOfWeek.ToString();
                var dateOnly = DateOnly.FromDateTime(date);

                var availability = await _context
                    .Availabilities.Where(a =>
                        a.ProviderId == providerId && !a.IsDeleted && a.DayOfWeek == dayOfWeekStr
                    )
                    .Include(a => a.AvailabilitySlots)
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                if (availability == null)
                    return Result.Success(
                        new AvailabilityResponse
                        {
                            Id = Guid.Empty,
                            Day = dayOfWeekStr,
                            Available = false,
                            Slots = new List<TimeSlotResponse>(),
                        }
                    );

                var isOnLeave = await _context.Leaves.AnyAsync(l =>
                    l.ProviderId == providerId
                    && !l.IsDeleted
                    && l.StartDate <= date
                    && l.EndDate >= date
                );

                if (isOnLeave || !availability.IsAvailable)
                    return Result.Success(
                        new AvailabilityResponse
                        {
                            Id = availability.Id,
                            Day = dayOfWeekStr,
                            Available = false,
                            Slots = new List<TimeSlotResponse>(),
                        }
                    );

                var availableSlots = await _scheduleService.GetAvailableSlotsForDateAsync(
                    providerId,
                    dateOnly
                );
                var slots = availableSlots
                    .Select(slot => new TimeSlotResponse
                    {
                        StartTime = slot.Start.ToString("HH:mm"),
                        EndTime = slot.End.ToString("HH:mm"),
                    })
                    .ToList();

                return Result.Success(
                    new AvailabilityResponse
                    {
                        Id = availability.Id,
                        Day = dayOfWeekStr,
                        Available = true,
                        Slots = slots,
                    }
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error retrieving provider availability for date {Date}",
                    date
                );
                return Result.Failure<AvailabilityResponse>(
                    Error.Internal("Error retrieving provider availability for date")
                );
            }
        }

        public async Task<
            Result<AvailabilityTemplateResponse>
        > GetProviderAvailabilityTemplateAsync(Guid providerId, DateTime? checkDate = null)
        {
            try
            {
                var availabilities = await _context
                    .Availabilities.Where(a => a.ProviderId == providerId && !a.IsDeleted)
                    .Include(cpc => cpc.CareProviderProfile)
                    .ThenInclude(cpc => cpc.CareProviderCategories)
                    .Include(a => a.AvailabilitySlots)
                    .OrderBy(a => a.DayOfWeek)
                    .AsNoTracking()
                    .ToListAsync();

                if (!availabilities.Any())
                    return Result.Success<AvailabilityTemplateResponse>(
                        new AvailabilityTemplateResponse()
                    );

                var targetDate = checkDate?.Date ?? DateTime.UtcNow.Date;
                var checkDateFrom = targetDate;
                var checkDateTo = targetDate.AddDays(7);

                var bufferDuration =
                    availabilities
                        .FirstOrDefault(p => p.ProviderId == providerId)
                        ?.CareProviderProfile?.BufferDuration ?? 0;

                var workingHours =
                    availabilities
                        .Where(p => p.ProviderId == providerId)
                        ?.Select(p => p.CareProviderProfile.WorkingHours)
                        .FirstOrDefault() ?? 0;

                var activeLeavePeriods = await _context
                    .Leaves.Where(l =>
                        l.ProviderId == providerId
                        && !l.IsDeleted
                        && l.StartDate <= checkDateTo
                        && l.EndDate >= checkDateFrom
                    )
                    .AsNoTracking()
                    .ToListAsync();

                var response = availabilities.Select(a =>
                {
                    var dayOfWeek = Enum.Parse<DayOfWeek>(a.DayOfWeek);
                    var isOnLeaveForThisDay = activeLeavePeriods.Any(leave =>
                    {
                        for (
                            var date = leave.StartDate;
                            date <= leave.EndDate;
                            date = date.AddDays(1)
                        )
                        {
                            if (
                                date.DayOfWeek == dayOfWeek
                                && date >= checkDateFrom
                                && date <= checkDateTo
                            )
                                return true;
                        }

                        return false;
                    });

                    var finalAvailability = a.IsAvailable && !isOnLeaveForThisDay;
                    return new AvailabilityResponse
                    {
                        Id = a.Id,
                        Day = a.DayOfWeek,
                        Available = finalAvailability,
                        Slots = a
                            .AvailabilitySlots.Select(s => new TimeSlotResponse
                            {
                                StartTime = s.StartTime.ToString("HH:mm"),
                                EndTime = s.EndTime.ToString("HH:mm"),
                            })
                            .ToList(),
                    };
                });

                var templateResponse = new AvailabilityTemplateResponse
                {
                    Availabilities = response,
                    BufferDuration = bufferDuration,
                    WorkingHoursPerDay = workingHours,
                };

                return templateResponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving provider availability template");
                return Result.Failure<AvailabilityTemplateResponse>(
                    Error.Internal("Error retrieving provider availability template")
                );
            }
        }

        public async Task<Result> UpdateAvailabilityStatusAsync(
            Guid userId,
            Guid availabilityId,
            bool isAvailable
        )
        {
            try
            {
                var availability = await _context.Availabilities.FirstOrDefaultAsync(a =>
                    a.Id == availabilityId && !a.IsDeleted
                );
                if (availability == null)
                    return Result.Failure(Error.NotFound("Availability record not found"));

                var providerProfile = await _context
                    .CareProviderProfiles.AsNoTracking()
                    .FirstOrDefaultAsync(p => p.Id == availability.ProviderId && !p.IsDeleted);
                if (providerProfile == null || providerProfile.UserId != userId)
                    return Result.Failure(
                        Error.Unauthorized("You do not have permission to update this availability")
                    );

                availability.IsAvailable = isAvailable;
                availability.UpdatedAt = DateTime.UtcNow;
                availability.UpdatedBy = userId;

                await _context.SaveChangesAsync();
                _logger.LogInformation(
                    "Availability {AvailabilityId} status updated to {IsAvailable} by user {UserId}",
                    availabilityId,
                    isAvailable,
                    userId
                );
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error updating availability status for {AvailabilityId}",
                    availabilityId
                );
                return Result.Failure(Error.Internal("Error updating availability status"));
            }
        }

        public async Task<
            Result<EnhancedAvailabilityResponse>
        > GetProviderEnhancedAvailabilityAsync(Guid providerId, int monthsAhead = 3)
        {
            try
            {
                var startDate = DateTime.UtcNow.Date;
                var endDate = startDate.AddMonths(monthsAhead);

                var availabilities = await _context
                    .Availabilities.Where(a => a.ProviderId == providerId && !a.IsDeleted)
                    .Include(a => a.AvailabilitySlots)
                    .OrderBy(a => a.DayOfWeek)
                    .AsNoTracking()
                    .ToListAsync();

                if (!availabilities.Any())
                    return Result.Success(new EnhancedAvailabilityResponse());

                var leavePeriods = await _context
                    .Leaves.Where(l =>
                        l.ProviderId == providerId
                        && !l.IsDeleted
                        && l.StartDate <= endDate
                        && l.EndDate >= startDate
                    )
                    .AsNoTracking()
                    .ToListAsync();

                var availableSlots = new Dictionary<string, List<TimeSlotResponse>>();
                foreach (var availability in availabilities)
                {
                    var slots = availability.IsAvailable
                        ? availability
                            .AvailabilitySlots.Select(s => new TimeSlotResponse
                            {
                                StartTime = s.StartTime.ToString("HH:mm"),
                                EndTime = s.EndTime.ToString("HH:mm"),
                            })
                            .ToList()
                        : new List<TimeSlotResponse>();
                    availableSlots[availability.DayOfWeek] = slots;
                }

                var availableDaysByMonth = new List<MonthlyAvailabilityResponse>();
                var currentDate = startDate;

                while (currentDate <= endDate)
                {
                    var monthKey = $"{currentDate:MMMM}";
                    var year = currentDate.Year;
                    var dateOnly = DateOnly.FromDateTime(currentDate);

                    var monthlyResponse = availableDaysByMonth.FirstOrDefault(m =>
                        m.Month == monthKey && m.Year == year
                    );
                    if (monthlyResponse == null)
                    {
                        monthlyResponse = new MonthlyAvailabilityResponse
                        {
                            Month = monthKey,
                            Year = year,
                            Days = new List<AvailableDayResponse>(),
                        };
                        availableDaysByMonth.Add(monthlyResponse);
                    }

                    var isOnLeave = leavePeriods.Any(l =>
                        l.StartDate <= currentDate && l.EndDate >= currentDate
                    );
                    var dayAvailability = availabilities.FirstOrDefault(a =>
                        a.DayOfWeek == currentDate.DayOfWeek.ToString()
                    );
                    if (!isOnLeave && dayAvailability != null && dayAvailability.IsAvailable)
                    {
                        var slots = await _scheduleService.GetAvailableSlotsForDateAsync(
                            providerId,
                            dateOnly
                        );
                        if (slots.Any())
                        {
                            monthlyResponse.Days.Add(
                                new AvailableDayResponse
                                {
                                    Date = currentDate.ToString("yyyy-MM-dd"),
                                    DayOfWeek = currentDate.DayOfWeek.ToString(),
                                }
                            );
                        }
                    }

                    currentDate = currentDate.AddDays(1);
                }

                var response = new EnhancedAvailabilityResponse
                {
                    AvailableSlots = availableSlots,
                    AvailableDaysByMonth = availableDaysByMonth,
                };

                return Result.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving provider enhanced availability");
                return Result.Failure<EnhancedAvailabilityResponse>(
                    Error.Internal("Error retrieving provider enhanced availability")
                );
            }
        }

        public async Task<Result<Guid>> AddLeaveAsync(Guid providerId, LeaveRequest request)
        {
            try
            {
                var providerExists = await _context
                    .CareProviderProfiles.AsNoTracking()
                    .AnyAsync(p => p.Id == providerId && !p.IsDeleted);
                if (!providerExists)
                    return Result.Failure<Guid>(Error.NotFound("Provider profile not found"));

                if (request.EndDate < request.StartDate)
                    return Result.Failure<Guid>(
                        Error.Validation("End date must be after start date")
                    );

                if (request.StartDate < DateTime.UtcNow.Date)
                    return Result.Failure<Guid>(
                        Error.Validation("Start date cannot be in the past")
                    );

                var hasOverlappingLeave = await HasOverlappingLeaveAsync(
                    providerId,
                    request.StartDate.Date,
                    request.EndDate.Date
                );
                if (hasOverlappingLeave)
                    return Result.Failure<Guid>(
                        Error.Conflict(
                            "A leave period already exists that overlaps with the requested dates"
                        )
                    );

                var leave = new Leave
                {
                    Id = Guid.NewGuid(),
                    ProviderId = providerId,
                    StartDate = request.StartDate.Date,
                    EndDate = request.EndDate.Date,
                    Reason = request.Reason,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = providerId,
                };

                await _context.Leaves.AddAsync(leave);
                await _context.SaveChangesAsync();
                return Result.Success(leave.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding leave for provider {ProviderId}", providerId);
                return Result.Failure<Guid>(Error.Internal("Error adding leave"));
            }
        }

        public async Task<Result> UpdateLeaveAsync(
            Guid providerId,
            Guid leaveId,
            LeaveRequest request
        )
        {
            try
            {
                var providerExists = await _context
                    .CareProviderProfiles.AsNoTracking()
                    .AnyAsync(p => p.Id == providerId && !p.IsDeleted);
                if (!providerExists)
                    return Result.Failure(Error.NotFound("Provider profile not found"));

                var leave = await _context.Leaves.FirstOrDefaultAsync(l =>
                    l.Id == leaveId && l.ProviderId == providerId && !l.IsDeleted
                );
                if (leave == null)
                    return Result.Failure(
                        Error.NotFound("Leave not found or you don't have permission to update it")
                    );

                if (request.EndDate < request.StartDate)
                    return Result.Failure(Error.Validation("End date must be after start date"));

                if (request.StartDate < DateTime.UtcNow.Date)
                    return Result.Failure(Error.Validation("Start date cannot be in the past"));

                var hasOverlappingLeave = await HasOverlappingLeaveAsync(
                    providerId,
                    request.StartDate.Date,
                    request.EndDate.Date,
                    leaveId
                );
                if (hasOverlappingLeave)
                    return Result.Failure(
                        Error.Conflict(
                            "A leave period already exists that overlaps with the requested dates"
                        )
                    );

                leave.StartDate = request.StartDate.Date;
                leave.EndDate = request.EndDate.Date;
                leave.Reason = request.Reason;
                leave.UpdatedAt = DateTime.UtcNow;
                leave.UpdatedBy = providerId;

                await _context.SaveChangesAsync();
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating leave for provider {ProviderId}", providerId);
                return Result.Failure(Error.Internal("Error updating leave"));
            }
        }

        public async Task<Result> DeleteLeaveAsync(Guid providerId, Guid leaveId)
        {
            try
            {
                var providerExists = await _context
                    .CareProviderProfiles.AsNoTracking()
                    .AnyAsync(p => p.Id == providerId && !p.IsDeleted);
                if (!providerExists)
                    return Result.Failure(Error.NotFound("Provider profile not found"));

                var leave = await _context.Leaves.FirstOrDefaultAsync(l =>
                    l.Id == leaveId && l.ProviderId == providerId && !l.IsDeleted
                );
                if (leave == null)
                    return Result.Failure(
                        Error.NotFound("Leave not found or you don't have permission to delete it")
                    );

                leave.IsDeleted = true;
                leave.DeletedAt = DateTime.UtcNow;
                leave.DeletedBy = providerId;

                await _context.SaveChangesAsync();
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting leave for provider {ProviderId}", providerId);
                return Result.Failure(Error.Internal("Error deleting leave"));
            }
        }

        public async Task<Result<LeaveResponse>> GetLeaveAsync(Guid providerId, Guid leaveId)
        {
            try
            {
                var providerExists = await _context
                    .CareProviderProfiles.AsNoTracking()
                    .AnyAsync(p => p.Id == providerId && !p.IsDeleted);
                if (!providerExists)
                    return Result.Failure<LeaveResponse>(
                        Error.NotFound("Provider profile not found")
                    );

                var leave = await _context.Leaves.FirstOrDefaultAsync(l =>
                    l.Id == leaveId && l.ProviderId == providerId && !l.IsDeleted
                );
                if (leave == null)
                    return Result.Failure<LeaveResponse>(
                        Error.NotFound("Leave not found or you don't have permission to view it")
                    );

                var response = new LeaveResponse
                {
                    Id = leave.Id,
                    ProviderId = leave.ProviderId,
                    StartDate = leave.StartDate,
                    EndDate = leave.EndDate,
                    Reason = leave.Reason,
                    CreatedAt = leave.CreatedAt,
                };

                return Result.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error retrieving leave for provider {ProviderId}",
                    providerId
                );
                return Result.Failure<LeaveResponse>(Error.Internal("Error retrieving leave"));
            }
        }

        public async Task<Result<IEnumerable<LeaveResponse>>> GetMyLeavesAsync(Guid userId)
        {
            try
            {
                var providerProfile = await _context
                    .CareProviderProfiles.AsNoTracking()
                    .FirstOrDefaultAsync(p => p.UserId == userId && !p.IsDeleted);
                if (providerProfile == null)
                    return Result.Failure<IEnumerable<LeaveResponse>>(
                        Error.NotFound("Provider profile not found")
                    );

                return await GetProviderLeavesAsync(providerProfile.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving leaves for current user");
                return Result.Failure<IEnumerable<LeaveResponse>>(
                    Error.Internal("Error retrieving leaves")
                );
            }
        }

        public async Task<Result<IEnumerable<LeaveResponse>>> GetProviderLeavesAsync(
            Guid providerId
        )
        {
            try
            {
                var providerExists = await _context
                    .CareProviderProfiles.AsNoTracking()
                    .AnyAsync(p => p.Id == providerId && !p.IsDeleted);
                if (!providerExists)
                    return Result.Failure<IEnumerable<LeaveResponse>>(
                        Error.NotFound("Provider profile not found")
                    );

                var leaves = await _context
                    .Leaves.Where(l => l.ProviderId == providerId && !l.IsDeleted)
                    .OrderBy(l => l.StartDate)
                    .AsNoTracking()
                    .ToListAsync();

                var response = leaves.Select(l => new LeaveResponse
                {
                    Id = l.Id,
                    ProviderId = l.ProviderId,
                    StartDate = l.StartDate,
                    EndDate = l.EndDate,
                    Reason = l.Reason,
                    CreatedAt = l.CreatedAt,
                });

                return Result.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error retrieving leaves for provider {ProviderId}",
                    providerId
                );
                return Result.Failure<IEnumerable<LeaveResponse>>(
                    Error.Internal("Error retrieving leaves")
                );
            }
        }

        public async Task<
            Result<(IEnumerable<LeaveResponse> Items, PaginationMetadata Pagination)>
        > GetAllLeavesAsync(LeaveListParams parameters)
        {
            try
            {
                var query = _context
                    .Leaves.Where(l => !l.IsDeleted)
                    .Include(l => l.CareProviderProfile)
                    .AsNoTracking()
                    .AsQueryable();

                if (parameters.ProviderId.HasValue)
                    query = query.Where(l => l.ProviderId == parameters.ProviderId.Value);

                if (parameters.StartDateFrom.HasValue)
                    query = query.Where(l => l.StartDate >= parameters.StartDateFrom.Value.Date);

                if (parameters.StartDateTo.HasValue)
                    query = query.Where(l => l.StartDate <= parameters.StartDateTo.Value.Date);

                if (parameters.EndDateFrom.HasValue)
                    query = query.Where(l => l.EndDate >= parameters.EndDateFrom.Value.Date);

                if (parameters.EndDateTo.HasValue)
                    query = query.Where(l => l.EndDate <= parameters.EndDateTo.Value.Date);

                if (parameters.DateFrom.HasValue && parameters.DateTo.HasValue)
                {
                    var dateFrom = parameters.DateFrom.Value.Date;
                    var dateTo = parameters.DateTo.Value.Date;
                    query = query.Where(l => l.StartDate <= dateTo && l.EndDate >= dateFrom);
                }
                else if (parameters.DateFrom.HasValue)
                {
                    var dateFrom = parameters.DateFrom.Value.Date;
                    query = query.Where(l => l.EndDate >= dateFrom);
                }
                else if (parameters.DateTo.HasValue)
                {
                    var dateTo = parameters.DateTo.Value.Date;
                    query = query.Where(l => l.StartDate <= dateTo);
                }

                if (!string.IsNullOrWhiteSpace(parameters.SortBy))
                {
                    switch (parameters.SortBy.ToLower())
                    {
                        case "startdate":
                            query = parameters.SortDescending
                                ? query.OrderByDescending(l => l.StartDate)
                                : query.OrderBy(l => l.StartDate);
                            break;
                        case "enddate":
                            query = parameters.SortDescending
                                ? query.OrderByDescending(l => l.EndDate)
                                : query.OrderBy(l => l.EndDate);
                            break;
                        case "createdat":
                            query = parameters.SortDescending
                                ? query.OrderByDescending(l => l.CreatedAt)
                                : query.OrderBy(l => l.CreatedAt);
                            break;
                        default:
                            query = query.OrderBy(l => l.StartDate);
                            break;
                    }
                }
                else
                {
                    query = query.OrderBy(l => l.StartDate);
                }

                var totalCount = await query.CountAsync();
                var items = await query
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToListAsync();

                var paginationMetadata = new PaginationMetadata(
                    parameters.PageNumber,
                    parameters.PageSize,
                    totalCount,
                    (int)Math.Ceiling(totalCount / (double)parameters.PageSize)
                );

                var leaveResponses = items.Select(l => new LeaveResponse
                {
                    Id = l.Id,
                    ProviderId = l.ProviderId,
                    StartDate = l.StartDate,
                    EndDate = l.EndDate,
                    Reason = l.Reason,
                    CreatedAt = l.CreatedAt,
                });

                return Result.Success((leaveResponses, paginationMetadata));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all leaves");
                return Result.Failure<(IEnumerable<LeaveResponse>, PaginationMetadata)>(
                    Error.Internal("Error retrieving leaves")
                );
            }
        }

        private async Task<bool> HasOverlappingLeaveAsync(
            Guid providerId,
            DateTime startDate,
            DateTime endDate,
            Guid? excludeLeaveId = null
        )
        {
            var query = _context.Leaves.Where(l => l.ProviderId == providerId && !l.IsDeleted);

            if (excludeLeaveId.HasValue)
                query = query.Where(l => l.Id != excludeLeaveId.Value);

            return await query.AnyAsync(l => startDate <= l.EndDate && endDate >= l.StartDate);
        }
    }
}
