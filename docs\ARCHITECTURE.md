# SuperCareApp Architecture

This document provides a comprehensive overview of the `SuperCareApp` solution's architecture. The project follows the principles of Clean Architecture, separating concerns into distinct layers to promote maintainability, scalability, and testability.

## High-Level Architecture

The solution is divided into the following primary layers:

- **Domain:** The core of the application, containing business entities and logic.
- **Application:** Orchestrates the domain logic and handles application-specific use cases.
- **Persistence (Infrastructure):** Responsible for data access and other external services.
- **Presentation:** The user-facing layer, implemented as an ASP.NET Core API.
- **Tests:** Contains tests for the various layers of the application.

### Architecture Diagram

The following diagram illustrates the dependencies between these layers:

```mermaid
graph TD
    subgraph Presentation
        A[super-care-app]
    end
    subgraph Application
        B[SuperCareApp.Application]
    end
    subgraph Domain
        C[SuperCareApp.Domain]
    end
    subgraph Infrastructure
        D[SuperCareApp.Persistence]
    end
    subgraph Tests
        E[SuperCareApp.Tests]
    end

    A --> B
    B --> C
    D --> C
    B -- uses --> D
    E --> A
    E --> B
    E --> C
    E --> D
```

## Detailed Layer Breakdown

This section provides a more detailed look at the structure and responsibilities of each layer.

### 1. SuperCareApp.Domain

The `SuperCareApp.Domain` layer is the heart of the application. It contains the core business logic, entities, and value objects that are independent of any specific technology or framework.

- **`Entities/`**: This directory contains the core business objects of the application, such as `Booking`, `CareProviderProfile`, `UserProfile`, and `Invoice`. These classes represent the fundamental concepts of the domain.
- **`Enums/`**: Contains various enumerations used throughout the domain, such as `BookingStatus`, `UserRole`, and `PaymentStatus`. These provide strong typing for states and categories.
- **`Identity/`**: Defines the entities for authentication and authorization, built upon ASP.NET Core Identity. This includes `ApplicationUser`, `ApplicationRole`, and their related claims and tokens.
- **`Common/`**: Holds base classes, exceptions, and other common components that are shared across the domain.
- **`Validation/`**: Contains domain-specific validation logic.
- **`Constants/`**: Defines constants used within the domain layer.

### 2. SuperCareApp.Application

The `SuperCareApp.Application` layer contains the application-specific business logic. It orchestrates the domain models to perform use cases and is the entry point for the presentation layer.

- **`Common/Interfaces/`**: This is one of the most critical parts of the application layer. It defines the contracts (interfaces) for services that are implemented in the infrastructure layer. This follows the Dependency Inversion Principle, ensuring that the application layer does not depend on concrete implementations. Key interfaces include `IAuthService`, `IBookingService`, `IRepository`, and `IUnitOfWork`.
- **`Common/Models/`**: Contains the Data Transfer Objects (DTOs) used to pass data between layers. These models are tailored for specific use cases and help prevent over-exposing the domain entities. Examples include `BookingResponse`, `CreateUserProfileRequest`, and `AuthResponse`.
- **`Shared/`**: Holds shared logic and utilities that are specific to the application layer.
- **`DependencyInjection.cs`**: This file contains the service registration for the application layer, making it easy to manage dependencies using the built-in DI container in ASP.NET Core.

### 3. SuperCareApp.Persistence

The `SuperCareApp.Persistence` layer is the infrastructure layer of the application. It handles all external concerns, such as database access, file storage, and sending emails.

- **`Repositories/`**: Contains the concrete implementations of the repository interfaces defined in the application layer. It uses Entity Framework Core for data access and includes a generic repository pattern to reduce boilerplate code. The `UnitOfWork.cs` class is used to manage transactions and ensure data consistency.
- **`Services/`**: Implements the various service interfaces defined in the application layer. This includes services for authentication, sending emails, and interacting with third-party APIs. The use of the Command and Query Responsibility Segregation (CQRS) pattern is a key feature of this layer, with commands and queries clearly separated into their own folders.
- **`Context/`**: Contains the `DbContext` class, which represents the session with the database and allows for querying and saving data.
- **`Migrations/`**: Holds the database migration files, which are used to manage the database schema over time.
- **`Configurations/`**: Contains the Entity Framework Core configurations for the domain entities, defining how they map to the database tables.
- **`DependencyInjection.cs`**: This file is responsible for registering the services and repositories from the persistence layer with the dependency injection container.

### 4. super-care-app (Presentation)

The `super-care-app` project is the presentation layer of the application, implemented as an ASP.NET Core Web API. It is responsible for handling incoming HTTP requests, authenticating users, and returning appropriate responses.

- **`Controllers/`**: Contains the API controllers that define the application's endpoints. Each controller is responsible for a specific resource or feature, such as `BookingsController`, `CareCategoriesController`, and `AuthController`.
- **`Middleware/`**: Holds custom middleware components that are used to process incoming requests and outgoing responses. This can include middleware for logging, exception handling, and authentication.
- **`Extensions/`**: Contains extension methods for configuring services and middleware in the `Program.cs` file. This helps keep the startup code clean and organized.
- **`Filters/`**: Includes custom action filters that can be applied to controllers or actions to perform tasks such as validation or authorization.
- **`Swagger/`**: Contains configurations for Swagger/OpenAPI, which is used to generate interactive API documentation.
- **`appsettings.json`**: The main configuration file for the application, containing settings for the database connection, logging, and other services.
- **`Program.cs`**: The entry point of the application, where services are configured and the HTTP request pipeline is built.
