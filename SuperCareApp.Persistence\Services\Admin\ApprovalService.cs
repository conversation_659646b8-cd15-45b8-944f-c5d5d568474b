﻿using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Admin
{
    public class ApprovalService : IApprovalService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ApprovalService> _logger;

        public ApprovalService(IUnitOfWork unitOfWork, ILogger<ApprovalService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<Result<IEnumerable<Approval>>> GetPendingApprovalsAsync()
        {
            try
            {
                var approvalRepository = _unitOfWork.Repository<Approval>() as IApprovalRepository;
                if (approvalRepository == null)
                {
                    _logger.LogError("Failed to get approval repository from unit of work");
                    return Result.Failure<IEnumerable<Approval>>(
                        Error.Internal("Repository unavailable")
                    );
                }

                return await approvalRepository.GetPendingApprovalsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending approvals");
                return Result.Failure<IEnumerable<Approval>>(
                    Error.Internal("Error getting pending approvals")
                );
            }
        }

        public async Task<Result<IEnumerable<Approval>>> GetApprovalsByUserIdAsync(Guid userId)
        {
            try
            {
                var approvalRepository = _unitOfWork.Repository<Approval>() as IApprovalRepository;
                if (approvalRepository == null)
                {
                    _logger.LogError("Failed to get approval repository from unit of work");
                    return Result.Failure<IEnumerable<Approval>>(
                        Error.Internal("Repository unavailable")
                    );
                }

                return await approvalRepository.GetApprovalsByUserIdAsync(userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting approvals for user {UserId}", userId);
                return Result.Failure<IEnumerable<Approval>>(
                    Error.Internal($"Error getting approvals for user {userId}")
                );
            }
        }

        public async Task<Result<IEnumerable<Approval>>> GetApprovalsByTypeAsync(ApprovalType type)
        {
            try
            {
                var approvalRepository = _unitOfWork.Repository<Approval>() as IApprovalRepository;
                if (approvalRepository == null)
                {
                    _logger.LogError("Failed to get approval repository from unit of work");
                    return Result.Failure<IEnumerable<Approval>>(
                        Error.Internal("Repository unavailable")
                    );
                }

                return await approvalRepository.GetApprovalsByTypeAsync(type);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting approvals of type {ApprovalType}", type);
                return Result.Failure<IEnumerable<Approval>>(
                    Error.Internal($"Error getting approvals of type {type}")
                );
            }
        }

        public async Task<Result<Approval>> GetApprovalByIdAsync(Guid approvalId)
        {
            try
            {
                var approvalRepository = _unitOfWork.Repository<Approval>();
                var result = await approvalRepository.GetByIdAsync(approvalId);
                if (result.IsFailure)
                {
                    return Result.Failure<Approval>(result.Error);
                }

                return Result.Success(result.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting approval {ApprovalId}", approvalId);
                return Result.Failure<Approval>(
                    Error.Internal($"Error getting approval {approvalId}")
                );
            }
        }

        public async Task<Result<Approval>> CreateApprovalAsync(
            Guid userId,
            ApprovalType type,
            string? approvalData = null,
            Guid? relatedEntityId = null
        )
        {
            try
            {
                var approval = new Approval
                {
                    Id = Guid.NewGuid(),
                    UserId = userId,
                    ApprovalType = type,
                    ApprovalData = approvalData,
                    RelatedEntityId = relatedEntityId,
                    IsApproved = false, // Set to false instead of null for pending approvals
                    CreatedAt = DateTime.UtcNow,
                };

                // Begin transaction
                await _unitOfWork.BeginTransactionAsync();

                var approvalRepository = _unitOfWork.Repository<Approval>();
                var result = await approvalRepository.AddAsync(approval);
                await _unitOfWork.SaveChangesAsync();

                // Commit transaction
                await _unitOfWork.CommitTransactionAsync();

                _logger.LogInformation(
                    "Created approval {ApprovalId} for user {UserId} of type {ApprovalType}",
                    approval.Id,
                    userId,
                    type
                );

                return Result.Success(approval);
            }
            catch (Exception ex)
            {
                // Rollback transaction on error
                await _unitOfWork.RollbackTransactionAsync();

                _logger.LogError(
                    ex,
                    "Error creating approval for user {UserId} of type {ApprovalType}",
                    userId,
                    type
                );
                return Result.Failure<Approval>(
                    Error.Internal($"Error creating approval for user {userId}")
                );
            }
        }

        public async Task<Result<Approval>> ApproveAsync(
            Guid approvalId,
            Guid adminId,
            string? notes = null
        )
        {
            try
            {
                var approvalRepository = _unitOfWork.Repository<Approval>() as IApprovalRepository;
                if (approvalRepository == null)
                {
                    _logger.LogError("Failed to get approval repository from unit of work");
                    return Result.Failure<Approval>(Error.Internal("Repository unavailable"));
                }

                // Begin transaction
                await _unitOfWork.BeginTransactionAsync();

                var result = await approvalRepository.ApproveAsync(approvalId, adminId, notes);
                if (result.IsFailure)
                {
                    await _unitOfWork.RollbackTransactionAsync();
                    return Result.Failure<Approval>(result.Error);
                }

                // Commit transaction
                await _unitOfWork.CommitTransactionAsync();

                _logger.LogInformation(
                    "Approval {ApprovalId} approved by admin {AdminId}",
                    approvalId,
                    adminId
                );
                return Result.Success(result.Value);
            }
            catch (Exception ex)
            {
                // Rollback transaction on error
                await _unitOfWork.RollbackTransactionAsync();

                _logger.LogError(
                    ex,
                    "Error approving approval {ApprovalId} by admin {AdminId}",
                    approvalId,
                    adminId
                );
                return Result.Failure<Approval>(
                    Error.Internal($"Error approving approval {approvalId}")
                );
            }
        }

        public async Task<Result<Approval>> RejectAsync(
            Guid approvalId,
            Guid adminId,
            string rejectionReason,
            string? notes = null
        )
        {
            try
            {
                var approvalRepository = _unitOfWork.Repository<Approval>() as IApprovalRepository;
                if (approvalRepository == null)
                {
                    _logger.LogError("Failed to get approval repository from unit of work");
                    return Result.Failure<Approval>(Error.Internal("Repository unavailable"));
                }

                // Begin transaction
                await _unitOfWork.BeginTransactionAsync();

                var result = await approvalRepository.RejectAsync(
                    approvalId,
                    adminId,
                    rejectionReason,
                    notes
                );
                if (result.IsFailure)
                {
                    await _unitOfWork.RollbackTransactionAsync();
                    return Result.Failure<Approval>(result.Error);
                }

                // Commit transaction
                await _unitOfWork.CommitTransactionAsync();

                _logger.LogInformation(
                    "Approval {ApprovalId} rejected by admin {AdminId}",
                    approvalId,
                    adminId
                );
                return Result.Success(result.Value);
            }
            catch (Exception ex)
            {
                // Rollback transaction on error
                await _unitOfWork.RollbackTransactionAsync();

                _logger.LogError(
                    ex,
                    "Error rejecting approval {ApprovalId} by admin {AdminId}",
                    approvalId,
                    adminId
                );
                return Result.Failure<Approval>(
                    Error.Internal($"Error rejecting approval {approvalId}")
                );
            }
        }
    }
}
