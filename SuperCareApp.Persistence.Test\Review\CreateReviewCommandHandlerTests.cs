using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using SuperCare.Persistence.Services.Reviews.Commands;
using SuperCareApp.Application.Common.Interfaces;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Context;
using Xunit;

namespace SuperCareApp.Persistence.Test.Services.Reviews.Commands;

public class CreateReviewCommandHandlerTests
{
    [Fact]
    public async Task GivenValidRequest_WhenHandleIsCalled_ThenReviewIsCreatedAndSuccessResultReturned()
    {
        // Arrange - Given
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()) // Isolated per test
            .Options;

        var currentUserServiceMock = new Mock<ICurrentUserService>();
        var loggerMock = new Mock<ILogger<CreateReviewCommandHandler>>();

        var userId = Guid.NewGuid();
        var bookingId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        currentUserServiceMock.Setup(s => s.UserId).Returns(userId);
        currentUserServiceMock.Setup(s => s.IsAuthenticated).Returns(true);

        using var context = new ApplicationDbContext(options);

        // Seed booking with completed status
        var booking = new Booking
        {
            Id = bookingId,
            ClientId = userId,
            ProviderId = providerId,
            CategoryId = Guid.NewGuid(),
            TotalAmount = 100m,
            PlatformFee = 10m,
            ProviderAmount = 90m,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
        };

        var bookingStatus = new BookingStatus
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Status = BookingStatusType.Completed,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
        };

        context.Bookings.Add(booking);
        context.BookingStatuses.Add(bookingStatus);
        await context.SaveChangesAsync(CancellationToken.None);

        var handler = new CreateReviewCommandHandler(
            context,
            currentUserServiceMock.Object,
            loggerMock.Object
        );

        var command = new CreateReviewCommand(
            BookingId: bookingId,
            Note: "Great service!",
            Rating: 4.5m
        );

        // Act - When
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert - Then
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal("Great service!", result.Value.Comment);
        Assert.Equal(4.5m, result.Value.Rating);
        Assert.NotEqual(Guid.Empty, result.Value.ReviewId);
        Assert.Equal(bookingId, result.Value.BookingId);
        Assert.Equal(userId, result.Value.ReviewerId);
        Assert.Equal(providerId, result.Value.RevieweeId);

        var savedReview = await context.Reviews.FindAsync(result.Value.ReviewId);
        Assert.NotNull(savedReview);
        Assert.Equal(bookingId, savedReview.BookingId);
        Assert.Equal(userId, savedReview.ReviewerId);
        Assert.Equal(providerId, savedReview.RevieweeId);
        Assert.Equal(4.5m, savedReview.Rating);
        Assert.Equal("Great service!", savedReview.Comment);
    }

    [Fact]
    public async Task GivenBookingDoesNotExist_WhenHandleIsCalled_ThenFailureResultIsReturned()
    {
        // Arrange - Given
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        var currentUserServiceMock = new Mock<ICurrentUserService>();
        var loggerMock = new Mock<ILogger<CreateReviewCommandHandler>>();

        var nonExistentBookingId = Guid.NewGuid();
        currentUserServiceMock.Setup(s => s.UserId).Returns(Guid.NewGuid());
        currentUserServiceMock.Setup(s => s.IsAuthenticated).Returns(true);

        using var context = new ApplicationDbContext(options);
        var handler = new CreateReviewCommandHandler(
            context,
            currentUserServiceMock.Object,
            loggerMock.Object
        );

        var command = new CreateReviewCommand(
            BookingId: nonExistentBookingId,
            Note: "Good job",
            Rating: 5.0m
        );

        // Act - When
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert - Then
        Assert.False(result.IsSuccess);
        Assert.Contains("Booking not found", result.Error.Message);
        Assert.Equal("NotFound", result.Error.Code);
    }

    [Fact]
    public async Task GivenInvalidRating_WhenHandleIsCalled_ThenValidationFailureIsReturned()
    {
        // Arrange - Given
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        var currentUserServiceMock = new Mock<ICurrentUserService>();
        var loggerMock = new Mock<ILogger<CreateReviewCommandHandler>>();

        var userId = Guid.NewGuid();
        currentUserServiceMock.Setup(s => s.UserId).Returns(userId);
        currentUserServiceMock.Setup(s => s.IsAuthenticated).Returns(true);

        using var context = new ApplicationDbContext(options);

        var handler = new CreateReviewCommandHandler(
            context,
            currentUserServiceMock.Object,
            loggerMock.Object
        );

        var command = new CreateReviewCommand(
            BookingId: Guid.NewGuid(),
            Note: "Awesome",
            Rating: 6.0m // Invalid rating > 5
        );

        // Act - When
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert - Then
        Assert.False(result.IsSuccess);
        Assert.Contains("Rating must be between 0 and 5", result.Error.Message);
        Assert.Equal("Validation", result.Error.Code);
    }

    [Fact]
    public async Task GivenUnauthenticatedUser_WhenHandleIsCalled_ThenUnauthorizedFailureIsReturned()
    {
        // Arrange - Given
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        var currentUserServiceMock = new Mock<ICurrentUserService>();
        var loggerMock = new Mock<ILogger<CreateReviewCommandHandler>>();

        currentUserServiceMock.Setup(s => s.IsAuthenticated).Returns(false);
        currentUserServiceMock.Setup(s => s.UserId).Returns((Guid?)null);

        using var context = new ApplicationDbContext(options);

        var handler = new CreateReviewCommandHandler(
            context,
            currentUserServiceMock.Object,
            loggerMock.Object
        );

        var command = new CreateReviewCommand(
            BookingId: Guid.NewGuid(),
            Note: "Great service!",
            Rating: 4.5m
        );

        // Act - When
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert - Then
        Assert.False(result.IsSuccess);
        Assert.Contains("User must be authenticated to create a review", result.Error.Message);
        Assert.Equal("Unauthorized", result.Error.Code);
    }

    [Fact]
    public async Task GivenBookingNotCompleted_WhenHandleIsCalled_ThenBadRequestFailureIsReturned()
    {
        // Arrange - Given
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        var currentUserServiceMock = new Mock<ICurrentUserService>();
        var loggerMock = new Mock<ILogger<CreateReviewCommandHandler>>();

        var userId = Guid.NewGuid();
        var bookingId = Guid.NewGuid();

        currentUserServiceMock.Setup(s => s.UserId).Returns(userId);
        currentUserServiceMock.Setup(s => s.IsAuthenticated).Returns(true);

        using var context = new ApplicationDbContext(options);

        // Seed booking with non-completed status
        var booking = new Booking
        {
            Id = bookingId,
            ClientId = userId,
            ProviderId = Guid.NewGuid(),
            CategoryId = Guid.NewGuid(),
            TotalAmount = 100m,
            PlatformFee = 10m,
            ProviderAmount = 90m,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
        };

        var bookingStatus = new BookingStatus
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Status = BookingStatusType.InProgress, // Not completed
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
        };

        context.Bookings.Add(booking);
        context.BookingStatuses.Add(bookingStatus);
        await context.SaveChangesAsync(CancellationToken.None);

        var handler = new CreateReviewCommandHandler(
            context,
            currentUserServiceMock.Object,
            loggerMock.Object
        );

        var command = new CreateReviewCommand(
            BookingId: bookingId,
            Note: "Great service!",
            Rating: 4.5m
        );

        // Act - When
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert - Then
        Assert.False(result.IsSuccess);
        Assert.Contains("Can only review completed bookings", result.Error.Message);
        Assert.Equal("BadRequest", result.Error.Code);
    }

    [Fact]
    public async Task GivenUserNotPartOfBooking_WhenHandleIsCalled_ThenForbiddenFailureIsReturned()
    {
        // Arrange - Given
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        var currentUserServiceMock = new Mock<ICurrentUserService>();
        var loggerMock = new Mock<ILogger<CreateReviewCommandHandler>>();

        var userId = Guid.NewGuid();
        var bookingId = Guid.NewGuid();
        var clientId = Guid.NewGuid(); // Different from userId
        var providerId = Guid.NewGuid(); // Different from userId

        currentUserServiceMock.Setup(s => s.UserId).Returns(userId);
        currentUserServiceMock.Setup(s => s.IsAuthenticated).Returns(true);

        using var context = new ApplicationDbContext(options);

        // Seed booking where user is neither client nor provider
        var booking = new Booking
        {
            Id = bookingId,
            ClientId = clientId,
            ProviderId = providerId,
            CategoryId = Guid.NewGuid(),
            TotalAmount = 100m,
            PlatformFee = 10m,
            ProviderAmount = 90m,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
        };

        var bookingStatus = new BookingStatus
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Status = BookingStatusType.Completed,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
        };

        context.Bookings.Add(booking);
        context.BookingStatuses.Add(bookingStatus);
        await context.SaveChangesAsync(CancellationToken.None);

        var handler = new CreateReviewCommandHandler(
            context,
            currentUserServiceMock.Object,
            loggerMock.Object
        );

        var command = new CreateReviewCommand(
            BookingId: bookingId,
            Note: "Great service!",
            Rating: 4.5m
        );

        // Act - When
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert - Then
        Assert.False(result.IsSuccess);
        Assert.Contains("You can only review bookings you are part of", result.Error.Message);
        Assert.Equal("Forbidden", result.Error.Code);
    }

    [Fact]
    public async Task GivenDuplicateReview_WhenHandleIsCalled_ThenConflictFailureIsReturned()
    {
        // Arrange - Given
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        var currentUserServiceMock = new Mock<ICurrentUserService>();
        var loggerMock = new Mock<ILogger<CreateReviewCommandHandler>>();

        var userId = Guid.NewGuid();
        var bookingId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        currentUserServiceMock.Setup(s => s.UserId).Returns(userId);
        currentUserServiceMock.Setup(s => s.IsAuthenticated).Returns(true);

        using var context = new ApplicationDbContext(options);

        // Seed booking with completed status
        var booking = new Booking
        {
            Id = bookingId,
            ClientId = userId,
            ProviderId = providerId,
            CategoryId = Guid.NewGuid(),
            TotalAmount = 100m,
            PlatformFee = 10m,
            ProviderAmount = 90m,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
        };

        var bookingStatus = new BookingStatus
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Status = BookingStatusType.Completed,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
        };

        // Seed existing review
        var existingReview = new Review
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            ReviewerId = userId,
            RevieweeId = providerId,
            Rating = 3.0m,
            Comment = "Previous review",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
        };

        context.Bookings.Add(booking);
        context.BookingStatuses.Add(bookingStatus);
        context.Reviews.Add(existingReview);
        await context.SaveChangesAsync(CancellationToken.None);

        var handler = new CreateReviewCommandHandler(
            context,
            currentUserServiceMock.Object,
            loggerMock.Object
        );

        var command = new CreateReviewCommand(
            BookingId: bookingId,
            Note: "Another review",
            Rating: 4.5m
        );

        // Act - When
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert - Then
        Assert.False(result.IsSuccess);
        Assert.Contains("You have already reviewed this booking", result.Error.Message);
        Assert.Equal("Conflict", result.Error.Code);
    }
}
