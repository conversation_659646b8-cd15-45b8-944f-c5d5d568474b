﻿using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;

namespace SuperCareApp.Persistence.Services.Bookings.Commands
{
    public record CreateBookingCommand(
        Guid UserId,
        Guid ProviderId,
        Guid CategoryId,
        DateTime StartDate,
        DateTime EndDate,
        TimeOnly? StartTime,
        TimeOnly? EndTime,
        string? SpecialInstructions
    ) : ICommand<Result<Guid>>;

    internal sealed class CreateBookingCommandHandler
        : ICommandHandler<CreateBookingCommand, Result<Guid>>
    {
        private readonly IBookingService _bookingService;

        public CreateBookingCommandHandler(IBookingService bookingService)
        {
            _bookingService = bookingService;
        }

        public async Task<Result<Guid>> Handle(
            CreateBookingCommand request,
            CancellationToken cancellationToken
        )
        {
            var booking = await _bookingService.CreateBookingAsync(
                request.UserId,
                request.ProviderId,
                request.CategoryId,
                request.StartDate,
                request.EndDate,
                request.StartTime,
                request.EndTime,
                request.SpecialInstructions
            );
            return booking;
        }
    }
}
