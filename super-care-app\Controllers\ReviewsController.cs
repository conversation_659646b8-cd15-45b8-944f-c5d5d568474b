using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using super_care_app.Shared.Constants;
using super_care_app.Shared.Utility;
using SuperCare.Persistence.Services.Reviews.Commands;
using SuperCareApp.Application.Common.Interfaces;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Persistence.Services.Reviews.Queries;

namespace super_care_app.Controllers;

[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<Error>))]
[ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ApiResponseModel<Error>))]
public class ReviewsController : BaseController
{
    private readonly IMediator _mediator;
    private readonly ICurrentUserService _currentUserService;
    private readonly RequestValidator _requestValidator;

    public ReviewsController(
        IMediator mediator,
        ICurrentUserService currentUserService,
        RequestValidator requestValidator
    )
    {
        _mediator = mediator;
        _currentUserService = currentUserService;
        _requestValidator = requestValidator;
    }

    [Authorize]
    [HttpPost]
    [Route(ApiRoutes.Reviews.CreateReview)]
    public async Task<IActionResult> AddReview(
        [FromRoute] Guid bookingId,
        [FromBody] CreateReviewRequest request
    )
    {
        var validationResult = await _requestValidator.ValidateAsync(
            request,
            new CreateReviewRequestValidator()
        );

        if (!validationResult.IsSuccess)
        {
            return ErrorResponseFromError<AuthResponse>(
                Error.Validation("Validation failed", validationResult.Error.ValidationErrors)
            );
        }

        var result = await _mediator.Send(
            new CreateReviewCommand(bookingId, request.Notes, request.Rating)
        );
        return SuccessResponse<object>(result.Value);
    }

    [Authorize]
    [HttpGet]
    [Route(ApiRoutes.Reviews.GetReview)]
    public async Task<IActionResult> GetReviewsByProvider([FromRoute] Guid providerId)
    {
        var result = await _mediator.Send(new GetReviewsByProviderQuery(providerId));
        if (result.IsFailure)
            return ErrorResponseFromError<AuthResponse>(result.Error);
        return SuccessResponse<object>(result.Value);
    }

    [Authorize]
    [HttpGet]
    [Route(ApiRoutes.Reviews.GetMyReviews)]
    public async Task<IActionResult> GetMyReviews()
    {
        var result = await _mediator.Send(new GetMyReviewsQuery());
        if (result.IsFailure)
            return ErrorResponseFromError<AuthResponse>(result.Error);
        return SuccessResponse<object>(result.Value);
    }
}
