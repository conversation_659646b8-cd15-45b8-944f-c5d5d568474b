﻿using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Bookings.Commands;

public class UpdateBookingStatusCommand : ICommand<Result>
{
    public Guid BookingId { get; set; }
    public BookingStatusType Status { get; set; }
    public string? Reason { get; set; } = string.Empty;

    public UpdateBookingStatusCommand(Guid bookingId, string status, string? reason = null)
    {
        BookingId = bookingId;
        Status = Enum.TryParse<BookingStatusType>(status, out var statusType)
            ? statusType
            : throw new ArgumentException("Invalid booking status");
        Reason = reason;
    }
}

internal sealed class UpdateBookingStatusCommandHandler
    : ICommandHandler<UpdateBookingStatusCommand, Result>
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<UpdateBookingStatusCommandHandler> _logger;
    private readonly ICurrentUserService _currentUserService;

    public UpdateBookingStatusCommandHandler(
        ApplicationDbContext context,
        ILogger<UpdateBookingStatusCommandHandler> logger,
        ICurrentUserService currentUserService
    )
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _currentUserService =
            currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
    }

    public async Task<Result> Handle(
        UpdateBookingStatusCommand request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            // Validate request
            if (request == null)
            {
                _logger.LogError("Update booking status request is null");
                return Result.Failure(Error.Validation("Request cannot be null"));
            }

            if (request.BookingId == Guid.Empty)
            {
                _logger.LogError("Invalid booking ID provided");
                return Result.Failure(Error.Validation("Booking ID is required"));
            }

            var currentUserId = _currentUserService.UserId;
            if (currentUserId == Guid.Empty)
            {
                _logger.LogError("Current user ID is invalid");
                return Result.Failure(Error.Unauthorized("Invalid user context"));
            }

            // Retrieve booking with tracking
            var booking = await _context
                .Bookings.Include(b => b.Status)
                .FirstOrDefaultAsync(b => b.Id == request.BookingId, cancellationToken);

            if (booking == null)
            {
                _logger.LogWarning("Booking with ID {BookingId} not found", request.BookingId);
                return Result.Failure(
                    Error.NotFound($"Booking with ID {request.BookingId} not found")
                );
            }

            // Authorization check
            bool isOwner = booking.ClientId == currentUserId;
            bool isProvider =
                _currentUserService.UserId.HasValue
                && _currentUserService.IsAuthenticated
                && await _context.CareProviderProfiles.AnyAsync(
                    p => p.UserId == _currentUserService.UserId && p.Id == booking.ProviderId,
                    cancellationToken
                );
            bool isAdmin = _currentUserService.IsInRole("Admin");

            if (!isOwner && !isProvider && !isAdmin)
            {
                _logger.LogWarning(
                    "User {UserId} attempted unauthorized status update for booking {BookingId}",
                    currentUserId,
                    request.BookingId
                );
                return Result.Failure(
                    Error.Forbidden("You do not have permission to modify this booking")
                );
            }

            // Status transition validation
            if (booking.Status.Status == request.Status)
            {
                _logger.LogWarning(
                    "Booking {BookingId} is already in status {Status}",
                    request.BookingId,
                    request.Status
                );
                return Result.Failure(
                    Error.Conflict($"Booking is already in {request.Status} status")
                );
            }

            // Business rule validation (example: can't move from Cancelled to Confirmed)
            if (
                booking.Status.Status == BookingStatusType.Cancelled
                && request.Status == BookingStatusType.Confirmed
            )
            {
                _logger.LogWarning(
                    "Invalid status transition from {CurrentStatus} to {NewStatus} for booking {BookingId}",
                    booking.Status.Status,
                    request.Status,
                    request.BookingId
                );
                return Result.Failure(
                    Error.Validation(
                        $"Cannot change status from {booking.Status.Status} to {request.Status}"
                    )
                );
            }

            // Update booking status
            booking.Status.Status = request.Status;
            booking.Status.Notes = request.Reason;
            booking.Status.UpdatedBy = currentUserId;
            booking.Status.UpdatedAt = DateTime.UtcNow;

            // Save changes with concurrency handling
            try
            {
                await _context.SaveChangesAsync(cancellationToken);
                _logger.LogInformation(
                    "Successfully updated booking {BookingId} to status {Status}",
                    request.BookingId,
                    request.Status
                );
                return Result.Success();
            }
            catch (DbUpdateConcurrencyException ex)
            {
                _logger.LogError(
                    ex,
                    "Concurrency conflict updating booking {BookingId}",
                    request.BookingId
                );
                return Result.Failure(
                    Error.Conflict(
                        "The booking was modified by another user. Please refresh and try again."
                            + ex.Message
                    )
                );
            }
            catch (DbUpdateException ex)
            {
                _logger.LogError(
                    ex,
                    "Database error updating booking {BookingId}",
                    request.BookingId
                );
                return Result.Failure(
                    Error.BadRequest("Failed to update booking due to database error" + ex.Message)
                );
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Update booking status operation was cancelled");
            return Result.Failure(Error.Internal("Operation was cancelled"));
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Unexpected error updating booking status for booking {BookingId}",
                request?.BookingId
            );
            return Result.Failure(
                Error.Internal(
                    "An unexpected error occurred while updating booking status :" + ex.Message
                )
            );
        }
    }
}
