﻿using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Categories
{
    /// <summary>
    /// Request model for updating a care category
    /// </summary>
    public class UpdateCareCategoryRequest
    {
        public string? Name { get; init; }
        public string? Description { get; init; }
        public bool? IsActive { get; init; }
        public decimal? HourlyRate { get; init; }
        public decimal? PlatformFee { get; init; }
        public string? Icon { get; init; }
        public string? Color { get; init; }
    }

    public class UpdateCareCategoryRequestValidation : AbstractValidator<UpdateCareCategoryRequest>
    {
        public UpdateCareCategoryRequestValidation()
        {
            RuleFor(r => r.Name)
                .NotEmpty()
                .WithMessage("Name is required")
                .MinimumLength(2)
                .WithMessage("Name must be at least 2 characters")
                .MaximumLength(100)
                .WithMessage("Name cannot exceed 100 characters")
                .Matches(@"^[\p{L}\p{M}\s'-]+$")
                .WithMessage("Name can only contain letters, apostrophes, hyphens, and spaces");

            RuleFor(x => x.Description)
                .MaximumLength(500)
                .WithMessage("Description cannot exceed 500 characters.")
                .When(x => x.Description != null);

            RuleFor(x => x.HourlyRate)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Hourly rate must be a non-negative value.");

            RuleFor(x => x.PlatformFee)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Platform fee must be a non-negative value.")
                .LessThanOrEqualTo(x => x.HourlyRate)
                .When(x => x.PlatformFee <= x.HourlyRate)
                .WithMessage("Platform fee cannot exceed hourly rate.");

            RuleFor(x => x.Icon)
                .MaximumLength(255)
                .WithMessage("Icon cannot exceed 255 characters.")
                .When(x => x.Icon != null);

            RuleFor(x => x.Color)
                .MaximumLength(7)
                .WithMessage("Color cannot exceed 7 characters.")
                .Matches(@"^#[0-9A-Fa-f]{6}$")
                .WithMessage("Color must be a valid hex color code (e.g., #FF5733).")
                .When(x => x.Color != null);
        }
    }
}
