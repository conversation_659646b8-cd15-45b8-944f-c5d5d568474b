﻿using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;

namespace SuperCareApp.Persistence.Services.Bookings.Commands
{
    public record DeleteBookingCommand(Guid Id, Guid BookingId) : ICommand<Result>;

    internal class DeleteBookingCommandHandler : I<PERSON>ommandHandler<DeleteBookingCommand, Result>
    {
        private readonly IBookingService _bookingService;

        public DeleteBookingCommandHandler(IBookingService bookingService)
        {
            _bookingService = bookingService;
        }

        public async Task<Result> Handle(
            DeleteBookingCommand request,
            CancellationToken cancellationToken
        )
        {
            var result = await _bookingService.DeleteBookingAsync(request.Id, request.BookingId);
            return result;
        }
    }
}
