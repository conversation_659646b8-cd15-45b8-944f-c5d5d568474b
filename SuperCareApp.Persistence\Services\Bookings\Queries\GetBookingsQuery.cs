﻿using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Bookings;

namespace SuperCareApp.Persistence.Services.Bookings.Queries
{
    public record GetBookingsQuery(BookingsRequest Request, BookingListParams PaginationParams)
        : IQuery<Result<PagedBookingList>>;

    internal class GetBookingsQueryHandler
        : IQueryHandler<GetBookingsQuery, Result<PagedBookingList>>
    {
        private readonly IBookingService _bookingService;

        public GetBookingsQueryHandler(IBookingService bookingService)
        {
            _bookingService = bookingService;
        }

        public async Task<Result<PagedBookingList>> Handle(
            GetBookingsQuery request,
            CancellationToken cancellationToken
        )
        {
            // Ensure valid pagination parameters
            if (request.PaginationParams.PageNumber < 1)
            {
                return Result.Failure<PagedBookingList>(
                    Error.BadRequest("Page number should be at least 1")
                );
            }
            return await _bookingService.GetAllBookingsAsync(
                request.Request,
                request.PaginationParams,
                cancellationToken
            );
        }
    }
}
