using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCare.Persistence.Services.Reviews.Commands;

public record CreateReviewCommand(Guid BookingId, string Note, decimal Rating)
    : ICommand<Result<CreateReviewResponse>>;

public sealed class CreateReviewCommandHandler
    : ICommandHandler<CreateReviewCommand, Result<CreateReviewResponse>>
{
    private readonly ApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<CreateReviewCommandHandler> _logger;

    public CreateReviewCommandHandler(
        ApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger<CreateReviewCommandHandler> logger
    )
    {
        _context = context;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<Result<CreateReviewResponse>> Handle(
        CreateReviewCommand request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            // Validate current user is authenticated
            if (!_currentUserService.IsAuthenticated || !_currentUserService.UserId.HasValue)
            {
                _logger.LogWarning(
                    "Unauthenticated user attempted to create review for booking {BookingId}",
                    request.BookingId
                );
                return Result.Failure<CreateReviewResponse>(
                    Error.Unauthorized("User must be authenticated to create a review")
                );
            }

            var currentUserId = _currentUserService.UserId.Value;

            // Validate rating range
            if (request.Rating < 0 || request.Rating > 5)
            {
                _logger.LogWarning(
                    "Invalid rating {Rating} provided for booking {BookingId}",
                    request.Rating,
                    request.BookingId
                );
                return Result.Failure<CreateReviewResponse>(
                    Error.Validation("Rating must be between 0 and 5")
                );
            }

            // Validate comment length
            if (!string.IsNullOrEmpty(request.Note) && request.Note.Length > 1000)
            {
                _logger.LogWarning(
                    "Comment too long ({Length} characters) for booking {BookingId}",
                    request.Note.Length,
                    request.BookingId
                );
                return Result.Failure<CreateReviewResponse>(
                    Error.Validation("Comment cannot exceed 1000 characters")
                );
            }

            // Get booking with related entities including provider profile
            var booking = await _context
                .Bookings.Include(b => b.Status)
                .Include(b => b.Provider) // Include the CareProviderProfile
                .FirstOrDefaultAsync(
                    b => b.Id == request.BookingId && !b.IsDeleted,
                    cancellationToken
                );

            if (booking == null)
            {
                _logger.LogWarning("Booking {BookingId} not found", request.BookingId);
                return Result.Failure<CreateReviewResponse>(Error.NotFound("Booking not found"));
            }

            // Validate booking is completed
            if (booking.Status?.Status != BookingStatusType.Completed)
            {
                _logger.LogWarning(
                    "Attempted to review booking {BookingId} with status {Status}",
                    request.BookingId,
                    booking.Status?.Status
                );
                return Result.Failure<CreateReviewResponse>(
                    Error.BadRequest("Can only review completed bookings")
                );
            }

            // Get the provider's user ID from the provider profile
            var providerUserId = booking.Provider?.UserId;
            if (providerUserId == null)
            {
                _logger.LogWarning(
                    "Provider profile not found for booking {BookingId}",
                    request.BookingId
                );
                return Result.Failure<CreateReviewResponse>(
                    Error.NotFound("Provider profile not found")
                );
            }

            // Validate user is part of the booking (either client or provider)
            if (booking.ClientId != currentUserId && providerUserId != currentUserId)
            {
                _logger.LogWarning(
                    "User {UserId} attempted to review booking {BookingId} they are not part of",
                    currentUserId,
                    request.BookingId
                );
                return Result.Failure<CreateReviewResponse>(
                    Error.Forbidden("You can only review bookings you are part of")
                );
            }

            // Determine reviewer and reviewee (both should be user IDs)
            var reviewerId = currentUserId;
            var revieweeId =
                booking.ClientId == currentUserId ? providerUserId.Value : booking.ClientId;

            // Validate that both reviewer and reviewee exist in the users table
            var reviewerExists = await _context.Users.AnyAsync(
                u => u.Id == reviewerId && !u.IsDeleted,
                cancellationToken
            );

            if (!reviewerExists)
            {
                _logger.LogWarning("Reviewer {ReviewerId} not found or deleted", reviewerId);
                return Result.Failure<CreateReviewResponse>(
                    Error.NotFound("Reviewer user not found")
                );
            }

            var revieweeExists = await _context.Users.AnyAsync(
                u => u.Id == revieweeId && !u.IsDeleted,
                cancellationToken
            );

            if (!revieweeExists)
            {
                _logger.LogWarning("Reviewee {RevieweeId} not found or deleted", revieweeId);
                return Result.Failure<CreateReviewResponse>(
                    Error.NotFound("Reviewee user not found")
                );
            }

            // Check if review already exists for this booking and reviewer
            var existingReview = await _context.Reviews.FirstOrDefaultAsync(
                r => r.BookingId == request.BookingId && r.ReviewerId == reviewerId && !r.IsDeleted,
                cancellationToken
            );

            if (existingReview != null)
            {
                _logger.LogWarning(
                    "Review already exists for booking {BookingId} by user {UserId}",
                    request.BookingId,
                    currentUserId
                );
                return Result.Failure<CreateReviewResponse>(
                    Error.Conflict("You have already reviewed this booking")
                );
            }

            // Create new review
            var review = new Review
            {
                Id = Guid.NewGuid(),
                BookingId = request.BookingId,
                ReviewerId = reviewerId,
                RevieweeId = revieweeId,
                Rating = request.Rating,
                Comment = string.IsNullOrWhiteSpace(request.Note) ? null : request.Note.Trim(),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
            };

            _context.Reviews.Add(review);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation(
                "Review {ReviewId} created successfully for booking {BookingId} by user {UserId}",
                review.Id,
                request.BookingId,
                currentUserId
            );

            var response = new CreateReviewResponse(
                review.Id,
                review.BookingId,
                review.ReviewerId,
                review.RevieweeId,
                review.Rating,
                review.Comment,
                review.CreatedAt
            );

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error creating review for booking {BookingId}",
                request.BookingId
            );
            return Result.Failure<CreateReviewResponse>(
                Error.Internal("An error occurred while creating the review")
            );
        }
    }
}
