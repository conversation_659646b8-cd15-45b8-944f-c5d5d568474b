﻿using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Repositories
{
    /// <summary>
    /// Repository implementation for care categories
    /// </summary>
    public class CareCategoryRepository : GenericRepository<CareCategory>, ICareCategoryRepository
    {
        public CareCategoryRepository(ApplicationDbContext dbContext)
            : base(dbContext) { }

        /// <inheritdoc />
        public async Task<Result<CareCategory>> GetByNameAsync(
            string name,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var category = await _dbSet
                    .AsNoTracking()
                    .FirstOrDefaultAsync(
                        c => c.Name.ToLower() == name.ToLower() && !c.IsDeleted,
                        cancellationToken
                    );

                if (category == null)
                {
                    return Result.Failure<CareCategory>(
                        Error.NotFound($"Care category with name '{name}' was not found.")
                    );
                }

                return Result.Success(category);
            }
            catch (Exception ex)
            {
                return Result.Failure<CareCategory>(Error.Internal(ex.Message));
            }
        }

        /// <inheritdoc />
        public async Task<Result<IEnumerable<CareCategory>>> GetActiveAsync(
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var categories = await _dbSet
                    .AsNoTracking()
                    .Where(c => c.IsActive && !c.IsDeleted)
                    .OrderBy(c => c.Name)
                    .ToListAsync(cancellationToken);

                return Result.Success<IEnumerable<CareCategory>>(categories);
            }
            catch (Exception ex)
            {
                return Result.Failure<IEnumerable<CareCategory>>(Error.Internal(ex.Message));
            }
        }

        /// <inheritdoc />
        public async Task<Result<IEnumerable<CareCategory>>> GetByProviderIdAsync(
            Guid providerId,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var categories = await _dbContext
                    .Set<CareProviderCategory>()
                    .AsNoTracking()
                    .Where(pc => pc.ProviderId == providerId && !pc.IsDeleted)
                    .Include(pc => pc.CareCategory)
                    .Where(pc => pc.CareCategory.IsActive && !pc.CareCategory.IsDeleted)
                    .Select(pc => pc.CareCategory)
                    .OrderBy(c => c.Name)
                    .ToListAsync(cancellationToken);

                return Result.Success<IEnumerable<CareCategory>>(categories);
            }
            catch (Exception ex)
            {
                return Result.Failure<IEnumerable<CareCategory>>(Error.Internal(ex.Message));
            }
        }

        /// <inheritdoc />
        public async Task<
            Result<(IEnumerable<CareCategory> Categories, int TotalCount)>
        > GetPaginatedAsync(
            int pageNumber,
            int pageSize,
            bool includeInactive = false,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var query = _dbSet.AsNoTracking().Where(c => !c.IsDeleted);

                if (!includeInactive)
                {
                    query = query.Where(c => c.IsActive);
                }

                // Get total count
                var totalCount = await query.CountAsync(cancellationToken);

                // Apply pagination
                var categories = await query
                    .OrderBy(c => c.Name)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync(cancellationToken);

                return Result.Success<(IEnumerable<CareCategory>, int)>((categories, totalCount));
            }
            catch (Exception ex)
            {
                return Result.Failure<(IEnumerable<CareCategory>, int)>(Error.Internal(ex.Message));
            }
        }

        /// <inheritdoc />
        public async Task<Result<bool>> ExistsByNameAsync(
            string name,
            Guid? excludeId = null,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var query = _dbSet
                    .AsNoTracking()
                    .Where(c => !c.IsDeleted && c.Name.ToLower() == name.ToLower());

                if (excludeId.HasValue)
                {
                    query = query.Where(c => c.Id != excludeId.Value);
                }

                var exists = await query.AnyAsync(cancellationToken);
                return Result.Success(exists);
            }
            catch (Exception ex)
            {
                return Result.Failure<bool>(Error.Internal(ex.Message));
            }
        }
    }
}
