using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Categories
{
    /// <summary>
    /// Request model for bulk updating care categories
    /// </summary>
    public class BulkUpdateCareCategoriesRequest
    {
        /// <summary>
        /// List of care category update items
        /// </summary>
        public List<CareCategoryUpdateItem> Categories { get; set; } = new();
    }

    /// <summary>
    /// Individual care category update item
    /// </summary>
    public class CareCategoryUpdateItem
    {
        /// <summary>
        /// The category ID to update
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// The category name (optional - only updated if provided)
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// The category description (optional - only updated if provided)
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Whether the category is active (optional - only updated if provided)
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// The hourly rate for this care category (optional - only updated if provided)
        /// </summary>
        public decimal? HourlyRate { get; set; }

        /// <summary>
        /// The platform fee for this care category (optional - only updated if provided)
        /// </summary>
        public decimal? PlatformFee { get; set; }
    }

    public class CreateCareCategoryRequestValidator : AbstractValidator<CreateCareCategoryRequest>
    {
        public CreateCareCategoryRequestValidator()
        {
            RuleFor(r => r.Name)
                .NotEmpty()
                .WithMessage("Name is required")
                .MinimumLength(2)
                .WithMessage("Name must be at least 2 characters")
                .MaximumLength(100)
                .WithMessage("Name cannot exceed 100 characters")
                .Matches(@"^[\p{L}\p{M}\s'-]+$")
                .WithMessage("Name can only contain letters, apostrophes, hyphens, and spaces");

            RuleFor(r => r.Description)
                .MaximumLength(500)
                .WithMessage("Description cannot exceed 500 characters");

            RuleFor(r => r.HourlyRate)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Hourly rate must be greater than or equal to 0");

            RuleFor(r => r.PlatformFee)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Platform fee must be greater than or equal to 0")
                .Must(HasValidDecimalPrecision)
                .WithMessage("Platform fee cannot have more than 6 decimal places");

            RuleFor(r => r.Icon)
                .MaximumLength(255)
                .WithMessage("Icon cannot exceed 255 characters.")
                .When(r => r.Icon != null);

            RuleFor(r => r.Color)
                .MaximumLength(7)
                .WithMessage("Color cannot exceed 7 characters.")
                .Matches(@"^#[0-9A-Fa-f]{6}$")
                .WithMessage("Color must be a valid hex color code (e.g., #FF5733).")
                .When(r => r.Color != null);
        }

        /// <summary>
        /// Validates that a decimal value has no more than 6 decimal places
        /// </summary>
        private static bool HasValidDecimalPrecision(decimal value)
        {
            var decimalPlaces = BitConverter.GetBytes(decimal.GetBits(value)[3])[2];
            return decimalPlaces <= 6;
        }
    }

    /// <summary>
    /// Validator for the BulkUpdateCareCategoriesRequest
    /// </summary>
    public class BulkUpdateCareCategoriesRequestValidator
        : AbstractValidator<BulkUpdateCareCategoriesRequest>
    {
        /// <summary>
        /// Constructor
        /// </summary>
        public BulkUpdateCareCategoriesRequestValidator()
        {
            RuleFor(x => x.Categories)
                .NotNull()
                .WithMessage("Categories list cannot be null")
                .NotEmpty()
                .WithMessage("At least one category must be provided for bulk update")
                .Must(categories => categories.Count <= 50)
                .WithMessage("Cannot update more than 50 categories at once");

            // Validate each category update item
            RuleForEach(x => x.Categories).SetValidator(new CareCategoryUpdateItemValidator());

            // Ensure no duplicate category IDs
            RuleFor(x => x.Categories)
                .Must(categories =>
                    categories.Select(c => c.Id).Distinct().Count() == categories.Count
                )
                .WithMessage("Duplicate category IDs are not allowed in bulk update")
                .When(x => x.Categories != null && x.Categories.Any());

            // Ensure no duplicate category names within the same request
            RuleFor(x => x.Categories)
                .Must(categories =>
                {
                    var namesWithValues = categories
                        .Where(c => !string.IsNullOrWhiteSpace(c.Name))
                        .Select(c => c.Name.ToLower())
                        .ToList();
                    return namesWithValues.Distinct().Count() == namesWithValues.Count;
                })
                .WithMessage(
                    "Duplicate category names are not allowed in the same bulk update request"
                )
                .When(x => x.Categories != null && x.Categories.Any());

            // Ensure at least one field is provided for update in each item
            RuleForEach(x => x.Categories)
                .Must(HasAtLeastOneUpdateField)
                .WithMessage("At least one field must be provided for update in each category item")
                .When(x => x.Categories != null);
        }

        /// <summary>
        /// Checks if at least one update field is provided
        /// </summary>
        private static bool HasAtLeastOneUpdateField(CareCategoryUpdateItem category)
        {
            return !string.IsNullOrWhiteSpace(category.Name)
                || !string.IsNullOrWhiteSpace(category.Description)
                || category.IsActive.HasValue
                || category.HourlyRate.HasValue
                || category.PlatformFee.HasValue;
        }
    }

    /// <summary>
    /// Validator for individual CareCategoryUpdateItem
    /// </summary>
    public class CareCategoryUpdateItemValidator : AbstractValidator<CareCategoryUpdateItem>
    {
        /// <summary>
        /// Constructor
        /// </summary>
        public CareCategoryUpdateItemValidator()
        {
            RuleFor(x => x.Id).NotEmpty().WithMessage("Category ID is required");

            RuleFor(x => x.Name)
                .Length(2, 100)
                .WithMessage("Name must be between 2 and 100 characters")
                .When(x => !string.IsNullOrWhiteSpace(x.Name));

            RuleFor(x => x.Description)
                .MaximumLength(500)
                .WithMessage("Description cannot exceed 500 characters")
                .When(x => !string.IsNullOrWhiteSpace(x.Description));

            RuleFor(x => x.HourlyRate)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Hourly rate must be greater than or equal to 0")
                .When(x => x.HourlyRate.HasValue);

            RuleFor(x => x.PlatformFee)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Platform fee must be greater than or equal to 0")
                .Must(fee => !fee.HasValue || HasValidDecimalPrecision(fee.Value))
                .WithMessage("Platform fee cannot have more than 6 decimal places")
                .When(x => x.PlatformFee.HasValue);
        }

        /// <summary>
        /// Validates that a decimal value has no more than 6 decimal places
        /// </summary>
        private static bool HasValidDecimalPrecision(decimal value)
        {
            var decimalPlaces = BitConverter.GetBytes(decimal.GetBits(value)[3])[2];
            return decimalPlaces <= 6;
        }
    }
}
