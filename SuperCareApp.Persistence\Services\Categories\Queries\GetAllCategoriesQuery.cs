﻿using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Categories;

namespace SuperCareApp.Persistence.Services.Categories.Queries
{
    /// <summary>
    /// Query to get all care categories
    /// </summary>
    public record GetAllCategoriesQuery(bool IncludeInactive = false, Guid? ProviderId = null)
        : IQuery<Result<IEnumerable<CareCategoryResponse>>>;

    /// <summary>
    /// Handler for the GetAllCategoriesQuery
    /// </summary>
    internal sealed class GetAllCategoriesQueryHandler
        : IQueryHandler<GetAllCategoriesQuery, Result<IEnumerable<CareCategoryResponse>>>
    {
        private readonly ICareCategoryService _categoryService;
        private readonly ILogger<GetAllCategoriesQueryHandler> _logger;
        private readonly ApplicationDbContext _context;

        /// <summary>
        /// Constructor
        /// </summary>
        public GetAllCategoriesQueryHandler(
            ICareCategoryService categoryService,
            ILogger<GetAllCategoriesQueryHandler> logger,
            ApplicationDbContext context
        )
        {
            _categoryService = categoryService;
            _logger = logger;
            _context = context;
        }

        /// <summary>
        /// Handles the query
        /// </summary>
        public async Task<Result<IEnumerable<CareCategoryResponse>>> Handle(
            GetAllCategoriesQuery request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                #region  Commented Code
                /*if (request.ProviderId != null)
                {
                    var providerCategories = await _categoryService.GetCategoriesByProviderIdAsync(
                        request.ProviderId.Value,
                        cancellationToken
                    );

                    var allCategories = await _categoryService.GetAllCategoriesAsync(
                        request.IncludeInactive,
                        cancellationToken
                    );

                    //Match common categories with provider categories and update hourly rate and experience
                    foreach (var category in allCategories.Value)
                    {
                        var providerCategory = providerCategories.Value.FirstOrDefault(pc =>
                            pc.Id == category.Id
                        );

                        if (providerCategory != null)
                        {
                            category.HourlyRate = providerCategory.HourlyRate;
                            category.ExperienceYears = providerCategory.ExperienceYears;
                        }
                    }
                }

                return await _categoryService.GetAllCategoriesAsync(
                    request.IncludeInactive,
                    cancellationToken
                );*/
                #endregion

                var query = _context.CareCategories.AsQueryable();
                if (!request.IncludeInactive)
                {
                    query = query.Where(c => c.IsActive);
                }

                var providerId = request.ProviderId;

                var result = await query
                    .Select(c => new CareCategoryResponse
                    {
                        Id = c.Id,
                        Name = c.Name,
                        Description =
                            providerId != null
                                ? c
                                    .CareProviderCategories.Where(pcl =>
                                        pcl.ProviderId == providerId
                                    )
                                    .Select(pcl => pcl.ProviderSpecificDescription)
                                    .FirstOrDefault()
                                : c.Description,
                        IsActive = c.IsActive,
                        PlatformFee = c.PlatformFee,
                        CreatedAt = c.CreatedAt,
                        UpdatedAt = c.UpdatedAt,
                        HourlyRate =
                            providerId != null
                                ? c
                                    .CareProviderCategories.Where(pcl =>
                                        pcl.ProviderId == providerId
                                    )
                                    .Select(pcl => (decimal?)pcl.HourlyRate ?? 0)
                                    .FirstOrDefault()
                                : 0,
                        ExperienceYears =
                            providerId != null
                                ? c
                                    .CareProviderCategories.Where(pcl =>
                                        pcl.ProviderId == providerId
                                    )
                                    .Select(pcl => (int?)pcl.ExperienceYears ?? 0)
                                    .FirstOrDefault()
                                : 0,
                    })
                    .ToListAsync(cancellationToken);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all care categories");
                return Result.Failure<IEnumerable<CareCategoryResponse>>(
                    Error.Internal(ex.Message)
                );
            }
        }
    }
}
